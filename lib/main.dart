import 'dart:ui';

import 'package:flame/camera.dart';
import 'package:flame/collisions.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

// ----------------------------
// 1. Main Entry
// ----------------------------
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Space Shooter Game',
      theme: ThemeData(useMaterial3: true),
      home: const LoginPage(),
    );
  }
}

// ----------------------------
// 2. Login Page
// ----------------------------
class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.rocket_launch, size: 100, color: Colors.white),
            const SizedBox(height: 20),
            const Text(
              "Space Shooter",
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (_) => const HomePage()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(
                  horizontal: 40,
                  vertical: 15,
                ),
              ),
              child: const Text(
                "Enter Game",
                style: TextStyle(fontSize: 18, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ----------------------------
// 3. Home Page
// ----------------------------
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "Ready for Battle?",
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              "• Use arrow keys or tap to move\n• Avoid asteroids\n• Collect power-ups\n• Survive as long as possible!",
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const GamePage()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(
                  horizontal: 40,
                  vertical: 15,
                ),
              ),
              child: const Text(
                "Start Game",
                style: TextStyle(fontSize: 18, color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ----------------------------
// 4. Game Components
// ----------------------------

class Player extends SpriteComponent
    with HasKeyboardHandlerComponents, CollisionCallbacks {
  late Vector2 velocity;
  double speed = 200.0;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    size = Vector2(50, 50);
    velocity = Vector2.zero();
    add(RectangleHitbox());

    // Create a simple spaceship sprite using a rectangle
    sprite = await Sprite.load('player.png').catchError((_) async {
      // Fallback: create a simple colored rectangle
      final paint = Paint()..color = Colors.blue;
      final recorder = PictureRecorder();
      final canvas = Canvas(recorder);
      canvas.drawRect(Rect.fromLTWH(0, 0, 50, 50), paint);
      final picture = recorder.endRecording();
      final image = await picture.toImage(50, 50);
      return Sprite(image);
    });
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Apply velocity
    position += velocity * dt;

    // Keep player on screen
    position.x = position.x.clamp(0, parent!.size.x - size.x);
    position.y = position.y.clamp(0, parent!.size.y - size.y);
  }

  @override
  bool onKeyEvent(KeyEvent event, Set<LogicalKeyboardKey> keysPressed) {
    super.onKeyEvent(event, keysPressed);
    velocity = Vector2.zero();

    if (keysPressed.contains(LogicalKeyboardKey.arrowLeft)) {
      velocity.x = -speed;
    }
    if (keysPressed.contains(LogicalKeyboardKey.arrowRight)) {
      velocity.x = speed;
    }
    if (keysPressed.contains(LogicalKeyboardKey.arrowUp)) {
      velocity.y = -speed;
    }
    if (keysPressed.contains(LogicalKeyboardKey.arrowDown)) {
      velocity.y = speed;
    }

    return true;
  }

  @override
  bool onCollisionStart(
    Set<Vector2> intersectionPoints,
    PositionComponent other,
  ) {
    if (other is Asteroid) {
      (parent as SpaceShooterGame).gameOver();
      return true;
    } else if (other is PowerUp) {
      other.removeFromParent();
      (parent as SpaceShooterGame).addScore(10);
      return true;
    }
    return false;
  }
}

class Asteroid extends SpriteComponent with CollisionCallbacks {
  late Vector2 velocity;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    size = Vector2(40, 40);
    velocity = Vector2(0, 100 + Random().nextDouble() * 100);
    add(RectangleHitbox());

    // Create asteroid sprite
    sprite = await Sprite.load('asteroid.png').catchError((_) async {
      final paint = Paint()..color = Colors.brown;
      final recorder = PictureRecorder();
      final canvas = Canvas(recorder);
      canvas.drawRect(Rect.fromLTWH(0, 0, 40, 40), paint);
      final picture = recorder.endRecording();
      final image = await picture.toImage(40, 40);
      return Sprite(image);
    });
  }

  @override
  void update(double dt) {
    super.update(dt);
    position += velocity * dt;

    // Remove when off screen
    if (position.y > parent!.size.y) {
      removeFromParent();
    }
  }
}

class PowerUp extends SpriteComponent with CollisionCallbacks {
  late Vector2 velocity;

  @override
  Future<void> onLoad() async {
    super.onLoad();
    size = Vector2(30, 30);
    velocity = Vector2(0, 80);
    add(RectangleHitbox());

    // Create power-up sprite
    sprite = await Sprite.load('powerup.png').catchError((_) async {
      final paint = Paint()..color = Colors.yellow;
      final recorder = PictureRecorder();
      final canvas = Canvas(recorder);
      canvas.drawCircle(const Offset(15, 15), 15, paint);
      final picture = recorder.endRecording();
      final image = await picture.toImage(30, 30);
      return Sprite(image);
    });
  }

  @override
  void update(double dt) {
    super.update(dt);
    position += velocity * dt;

    // Remove when off screen
    if (position.y > parent!.size.y) {
      removeFromParent();
    }
  }
}

// ----------------------------
// 5. Main Game Class
// ----------------------------
class SpaceShooterGame extends FlameGame
    with HasKeyboardHandlerComponents, HasCollisionDetection {
  late Player player;
  late TextComponent scoreText;
  late TextComponent livesText;
  int score = 0;
  int lives = 3;
  double asteroidSpawnTimer = 0;
  double powerUpSpawnTimer = 0;
  bool isGameOver = false;

  @override
  Future<void> onLoad() async {
    super.onLoad();

    // Set up camera
    camera.viewport = FixedResolutionViewport(resolution: Vector2(400, 600));

    // Add collision detection
    add(ScreenHitbox());

    // Create background
    final background = RectangleComponent(
      size: size,
      paint: Paint()..color = Colors.black,
    );
    add(background);

    // Add stars background
    for (int i = 0; i < 50; i++) {
      final star = CircleComponent(
        radius: Random().nextDouble() * 2 + 1,
        position: Vector2(
          Random().nextDouble() * size.x,
          Random().nextDouble() * size.y,
        ),
        paint: Paint()..color = Colors.white,
      );
      add(star);
    }

    // Create player
    player = Player();
    player.position = Vector2(size.x / 2 - 25, size.y - 100);
    add(player);

    // Create UI
    scoreText = TextComponent(
      text: 'Score: 0',
      position: Vector2(10, 10),
      textRenderer: TextPaint(
        style: const TextStyle(color: Colors.white, fontSize: 20),
      ),
    );
    add(scoreText);

    livesText = TextComponent(
      text: 'Lives: 3',
      position: Vector2(10, 40),
      textRenderer: TextPaint(
        style: const TextStyle(color: Colors.white, fontSize: 20),
      ),
    );
    add(livesText);
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (isGameOver) return;

    // Spawn asteroids
    asteroidSpawnTimer += dt;
    if (asteroidSpawnTimer > 1.0) {
      spawnAsteroid();
      asteroidSpawnTimer = 0;
    }

    // Spawn power-ups
    powerUpSpawnTimer += dt;
    if (powerUpSpawnTimer > 5.0) {
      spawnPowerUp();
      powerUpSpawnTimer = 0;
    }
  }

  void spawnAsteroid() {
    final asteroid = Asteroid();
    asteroid.position = Vector2(Random().nextDouble() * (size.x - 40), -40);
    add(asteroid);
  }

  void spawnPowerUp() {
    final powerUp = PowerUp();
    powerUp.position = Vector2(Random().nextDouble() * (size.x - 30), -30);
    add(powerUp);
  }

  void addScore(int points) {
    score += points;
    scoreText.text = 'Score: $score';
  }

  void gameOver() {
    if (isGameOver) return;

    lives--;
    livesText.text = 'Lives: $lives';

    if (lives <= 0) {
      isGameOver = true;
      overlays.add('GameOver');
      pauseEngine();
    } else {
      // Reset player position
      player.position = Vector2(size.x / 2 - 25, size.y - 100);
    }
  }

  void restartGame() {
    score = 0;
    lives = 3;
    isGameOver = false;
    scoreText.text = 'Score: 0';
    livesText.text = 'Lives: 3';

    // Remove all asteroids and power-ups
    children.whereType<Asteroid>().forEach(
      (asteroid) => asteroid.removeFromParent(),
    );
    children.whereType<PowerUp>().forEach(
      (powerUp) => powerUp.removeFromParent(),
    );

    // Reset player
    player.position = Vector2(size.x / 2 - 25, size.y - 100);

    overlays.remove('GameOver');
    resumeEngine();
  }

  void showPauseMenu() {
    overlays.add('PauseMenu');
    pauseEngine();
  }

  void hidePauseMenu() {
    overlays.remove('PauseMenu');
    resumeEngine();
  }
}

// ----------------------------
// 6. Game Page with Overlays
// ----------------------------
class GamePage extends StatelessWidget {
  const GamePage({super.key});

  @override
  Widget build(BuildContext context) {
    final game = SpaceShooterGame();

    return Scaffold(
      body: Stack(
        children: [
          GameWidget(
            game: game,
            overlayBuilderMap: {
              'PauseMenu': (context, game) => Container(
                color: Colors.black54,
                child: Center(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            "Game Paused",
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: () {
                              (game as SpaceShooterGame).hidePauseMenu();
                            },
                            child: const Text("Resume"),
                          ),
                          const SizedBox(height: 10),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text("Exit Game"),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              'GameOver': (context, game) => Container(
                color: Colors.black54,
                child: Center(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            "Game Over!",
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            "Final Score: ${(game as SpaceShooterGame).score}",
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: () {
                              (game).restartGame();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                            ),
                            child: const Text("Play Again"),
                          ),
                          const SizedBox(height: 10),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text("Main Menu"),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            },
          ),
          // Control buttons for mobile
          Positioned(
            bottom: 20,
            left: 20,
            child: Column(
              children: [
                // Up button
                GestureDetector(
                  onTapDown: (_) => game.player.velocity.y = -game.player.speed,
                  onTapUp: (_) => game.player.velocity.y = 0,
                  onTapCancel: () => game.player.velocity.y = 0,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.white24,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_up,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    // Left button
                    GestureDetector(
                      onTapDown: (_) =>
                          game.player.velocity.x = -game.player.speed,
                      onTapUp: (_) => game.player.velocity.x = 0,
                      onTapCancel: () => game.player.velocity.x = 0,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: const Icon(
                          Icons.keyboard_arrow_left,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    // Right button
                    GestureDetector(
                      onTapDown: (_) =>
                          game.player.velocity.x = game.player.speed,
                      onTapUp: (_) => game.player.velocity.x = 0,
                      onTapCancel: () => game.player.velocity.x = 0,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: const Icon(
                          Icons.keyboard_arrow_right,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                // Down button
                GestureDetector(
                  onTapDown: (_) => game.player.velocity.y = game.player.speed,
                  onTapUp: (_) => game.player.velocity.y = 0,
                  onTapCancel: () => game.player.velocity.y = 0,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Colors.white24,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Pause button
          Positioned(
            top: 40,
            right: 20,
            child: FloatingActionButton(
              onPressed: () => game.showPauseMenu(),
              backgroundColor: Colors.white24,
              child: const Icon(Icons.pause, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
